from uuid import UUID

import factory
import faker
from factory import BUILD_STRATEGY
from factory.django import DjangoModelFactory

from apps.integrations.models import Integration, Technology
from apps.integrations.utils import get_technology_category
from factories.organization import OrganizationFactory

uuid_faker = factory.Faker("uuid4")

# Technology ID to name mapping from the migration
TECHNOLOGY_NAMES = {
    "absolute": "Absolute",
    "azure_ad": "Microsoft Entra ID (Azure AD)",
    "carbon_black": "Carbon Black",
    "cb_cloud": "Carbon Black Cloud Endpoint Standard",
    "cb_threat_hunter": "Carbon Black Threat Hunter",
    "cisco_duo": "Duo",
    "cisco_ise": "Cisco Ise AssetView",
    "commvault": "Commvault",
    "cortex_xdr": "Palo Alto Cortex XDR",
    "crowdstrike_falcon": "CrowdStrike Falcon",
    "cyberark_epm": "CyberArk Endpoint Privilege Manager",
    "cylance": "Cylance",
    "defender_atp": "Microsoft Defender for Endpoint",
    "extrahop_revealx_360": "ExtraHop RevealX 360",
    "falcon_em": "CrowdStrike Discover",
    "freshworks_service": "Freshworks Freshservice",
    "import_hosts": "Import Hosts",
    "infoblox_ddi": "Infoblox DDI",
    "ivanti_neurons": "Ivanti Neurons",
    "ivanti_pm": "Ivanti Patch Management",
    "jamf_pro": "Jamf Pro",
    "ms_intune": "Microsoft Intune",
    "netapp_ontap": "NetApp ONTAP",
    "qualys_gav": "Qualys Global AssetView",
    "qualys_vmpc": "Qualys VMDR",
    "rapid7_insightvm": "Rapid7 InsightVM",
    "s1_ranger": "SentinelOne Ranger",
    "sentinel_one": "SentinelOne",
    "servicenow_cmdb": "ServiceNow CMDB",
    "sevco_io": "Sevco",
    "solarwinds_sd": "SolarWinds Server & Application Monitor",
    "tanium_em": "Tanium",
    "tenable_io": "Tenable IO",
    "tm_vision_one": "Trend Micro Vision One",
    "ubiquity_unifi": "Ubiquity UniFi",
    "veeam": "Veeam",
    "veritas_alta_baas": "Veritas Alta BaaS",
    "vmware_aria": "VMware Aria",
    "zscaler": "Zscaler",
    "user_input": "(Internal) User Input",
    "demo_environment": "Demo Environment",
}


class TechnologyFactory(DjangoModelFactory):
    class Meta:
        model = Technology
        django_get_or_create = ('technology_id',)
        strategy = BUILD_STRATEGY

    technology_id = "crowdstrike_falcon"
    category = "endpoint_security"
    internal = False

    @factory.lazy_attribute
    def name(self):
        # Use technology name mapping based on technology_id
        return TECHNOLOGY_NAMES.get(self.technology_id, self.technology_id)

    @factory.lazy_attribute
    def category(self):
        # Use correct category based on technology_id
        from apps.integrations.utils import get_technology_category
        return get_technology_category(self.technology_id)

    @factory.lazy_attribute
    def internal(self):
        # Mark certain technologies as internal
        return self.technology_id in ["user_input", "demo_environment"]


class IntegrationFactory(DjangoModelFactory):
    class Meta:
        model = Integration
        strategy = BUILD_STRATEGY

    # Using a lazy function because the uuid_faker return a str instead of a UUID
    id = factory.LazyFunction(
        lambda: UUID(
            uuid_faker.evaluate(None, None, {"locale": faker.config.DEFAULT_LOCALE})
        )
    )
    technology = factory.SubFactory(TechnologyFactory)
    organization = factory.SubFactory(OrganizationFactory)
    enabled = True
    vulnerability_coverage_mode = Integration.CoverageMode.NOT_APPLICABLE

    @factory.lazy_attribute
    def category_id(self):
        # Use correct category based on technology_id
        return get_technology_category(self.technology.technology_id) if self.technology else "asset_source"

    @classmethod
    def create(cls, **kwargs):
        # Handle backward compatibility for technology_id parameter
        if 'technology_id' in kwargs:
            technology_id = kwargs.pop('technology_id')
            # Create or get the technology
            technology = TechnologyFactory.create(technology_id=technology_id)
            kwargs['technology'] = technology
        return super().create(**kwargs)

    @classmethod
    def build(cls, **kwargs):
        # Handle backward compatibility for technology_id parameter
        if 'technology_id' in kwargs:
            technology_id = kwargs.pop('technology_id')
            # Create or get the technology
            technology = TechnologyFactory.build(technology_id=technology_id)
            kwargs['technology'] = technology
        return super().build(**kwargs)
