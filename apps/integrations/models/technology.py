from django.db import models


class Technology(models.Model):
    """
    Technology model to store technology information.
    """
    technology_id = models.CharField(max_length=255, unique=True, primary_key=True)
    name = models.CharField(max_length=255)
    category = models.CharField(max_length=255)
    internal = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'integrations_technology'
        verbose_name = 'Technology'
        verbose_name_plural = 'Technologies'

    def __str__(self):
        return f"{self.name} ({self.technology_id})"

    def __repr__(self):
        return (
            f"Technology("
            f"technology_id={self.technology_id!r}, "
            f"name={self.name!r}, "
            f")"
        )
